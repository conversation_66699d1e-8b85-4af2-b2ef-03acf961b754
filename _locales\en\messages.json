{"extensionName": {"message": "Prompt Enhancer", "description": "The name of the extension."}, "extensionDescription": {"message": "Transform your prompts into gold - Make AI conversations more precise and efficient", "description": "The description of the extension."}, "welcomeMessage": {"message": "Welcome to Prompt Enhancer", "description": "Welcome message on the login screen."}, "loginPrompt": {"message": "Please log in securely with your Google account.", "description": "Prompt to log in."}, "loginButton": {"message": "Login with Google", "description": "Text for the login button."}, "mainTitle": {"message": "Prompt Enhancer", "description": "Main title on the side panel."}, "userInputPlaceholder": {"message": "Enter your idea here, or use voice input..."}, "optimizeButtonWithShortcut": {"message": "Optimize Input (Shortcut: TAB)"}, "languageChangeNote": {"message": "Language has been changed. The panel will now reload to apply changes."}, "settingsTitle": {"message": "Account <PERSON><PERSON>", "description": "Title for the settings dropdown."}, "accountLabel": {"message": "Account:", "description": "Label for the user's email."}, "languageLabel": {"message": "Language", "description": "Label for the language selector."}, "landingPageButton": {"message": "Product Page", "description": "Text for the button to open the product landing page."}, "logoutButton": {"message": "Logout", "description": "Text for the logout button."}, "currentUserInputTitle": {"message": "Current Input:", "description": "Title for the user input section."}, "voiceInputTitle": {"message": "Voice Input", "description": "Tooltip for the voice input button."}, "optimizeButton": {"message": "Optimize Input", "description": "Text for the optimize button."}, "structureButton": {"message": "Expert Mode", "description": "Text for the expert mode button."}, "structureButtonTooltip": {"message": "For complex tasks. Copy the expert mode content and send it as the first message to AI, then start your conversation.", "description": "Tooltip for the expert mode button."}, "generatedResultTitle": {"message": "Generated Result:", "description": "Title for the result section."}, "historyButtonTitle": {"message": "View History", "description": "Tooltip for the history button."}, "settingsButtonTitle": {"message": "Settings", "description": "Tooltip for the settings button."}, "emptyStateTip": {"message": "💡 Enter your idea above, or use voice input", "description": "Tip for empty state in result area."}, "exampleBeforeLabel": {"message": "Before:", "description": "Label for 'before optimization' in examples."}, "exampleAfterLabel": {"message": "After:", "description": "Label for 'after optimization' in examples."}, "example1BeforeText": {"message": "I want to lose weight", "description": "Before text for the first example."}, "example1AfterText": {"message": "Create a 3-month weight loss plan with diet, exercise, and tracking goals", "description": "After text for the first example."}, "tryExample1Button": {"message": "🏃‍♂️ Try this example", "description": "Button text for trying the first example."}, "example2BeforeText": {"message": "Help me write a resume", "description": "Before text for the second example."}, "example2AfterText": {"message": "Write a product manager resume highlighting 3 years experience, data analysis, UX design, and project management skills", "description": "After text for the second example."}, "tryExample2Button": {"message": "📝 Try this example", "description": "Button text for trying the second example."}, "historyTitle": {"message": "History", "description": "Title for the history dropdown."}, "clearHistoryButton": {"message": "Clear", "description": "Button text for clearing history."}, "confirmClearMessage": {"message": "Are you sure you want to clear all history?", "description": "Confirmation message for clearing history."}, "confirmCancelButton": {"message": "Cancel", "description": "Text for the cancel button in the confirmation dialog."}, "confirmOkButton": {"message": "OK"}, "languageEnglish": {"message": "English"}, "languageChineseSimplified": {"message": "Simplified Chinese"}, "themeLabel": {"message": "Theme"}, "themeLightMode": {"message": "Light Mode"}, "themeDarkMode": {"message": "Dark Mode"}, "themeAutoMode": {"message": "Follow System"}, "optimizeInputButton": {"message": "Optimize Input"}, "optimizeAgainButton": {"message": "Optimize Again"}, "optimizationLimitReached": {"message": "Optimization Limit Reached"}, "optimizing": {"message": "Optimizing..."}, "thinking": {"message": "Thinking..."}, "listening": {"message": "Listening..."}, "waitingForInput": {"message": "Waiting for user input..."}, "processingFailed": {"message": "Processing failed"}, "noHistoryMessage": {"message": "No history records."}, "clearHistoryFailed": {"message": "Failed to clear history."}, "firstOptimizationAnalysis": {"message": "Performing initial optimization and analysis..."}, "optimizedPromptTitle": {"message": "✨ Optimized Prompt:"}, "problemAnalysisTitle": {"message": "🔬 Problem Analysis:"}, "generatingOptimizedPrompt": {"message": "Generating optimized prompt..."}, "analyzingInput": {"message": "Analyzing input content..."}, "structuredPromptTitle": {"message": "✨ Expert Mode Prompt:"}}