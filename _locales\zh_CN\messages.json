{"extensionName": {"message": "点石成金", "description": "扩展程序的名称。"}, "extensionDescription": {"message": "智能提示词增强器 - 让您的AI对话更加精准高效", "description": "扩展程序的描述。"}, "welcomeMessage": {"message": "欢迎使用点石成金", "description": "登录界面的欢迎语。"}, "loginPrompt": {"message": "请使用您的 Google 账户安全登录。", "description": "提示登录的信息。"}, "loginButton": {"message": "使用 Google 登录", "description": "登录按钮的文本。"}, "mainTitle": {"message": "点石成金", "description": "侧边栏的主标题。"}, "userInputPlaceholder": {"message": "上方输入你的想法，或语音输入"}, "optimizeButtonWithShortcut": {"message": "优化输入内容（快捷键：TAB）"}, "languageChangeNote": {"message": "语言已切换，侧边栏将重新加载以应用更改。"}, "settingsTitle": {"message": "账户设置", "description": "设置下拉菜单的标题。"}, "accountLabel": {"message": "账户：", "description": "用户邮箱的标签。"}, "languageLabel": {"message": "语言", "description": "语言选择器的标签。"}, "landingPageButton": {"message": "产品主页", "description": "打开产品落地页的按钮文本。"}, "logoutButton": {"message": "登出", "description": "登出按钮的文本。"}, "currentUserInputTitle": {"message": "当前输入：", "description": "用户输入区域的标题。"}, "voiceInputTitle": {"message": "语音输入", "description": "语音输入按钮的提示文本。"}, "optimizeButton": {"message": "优化输入", "description": "优化按钮的文本。"}, "structureButton": {"message": "专家模式", "description": "专家模式按钮的文本。"}, "structureButtonTooltip": {"message": "适用复杂任务。复制专家模式内容，将其作为第一条消息发送给AI，然后开始对话。", "description": "专家模式按钮的提示信息。"}, "generatedResultTitle": {"message": "生成结果：", "description": "结果区域的标题。"}, "historyButtonTitle": {"message": "查看历史记录", "description": "历史记录按钮的提示文本。"}, "settingsButtonTitle": {"message": "设置", "description": "设置按钮的提示文本。"}, "emptyStateTip": {"message": "💡 上方输入你的想法，或语音输入", "description": "结果区域空状态下的提示。"}, "exampleBeforeLabel": {"message": "优化前：", "description": "示例中‘优化前’的标签。"}, "exampleAfterLabel": {"message": "优化后：", "description": "示例中‘优化后’的标签。"}, "example1BeforeText": {"message": "我想减肥", "description": "第一个示例的优化前文本。"}, "example1AfterText": {"message": "制定一个为期3个月的健康减重计划，目标减重10-15斤，包含饮食调整、运动安排和进度跟踪方案", "description": "第一个示例的优化后文本。"}, "tryExample1Button": {"message": "🏃‍♂️ 试试这个例子", "description": "尝试第一个示例的按钮文本。"}, "example2BeforeText": {"message": "帮我写简历", "description": "第二个示例的优化前文本。"}, "example2AfterText": {"message": "为3年工作经验的产品经理撰写简历，突出数据分析能力、用户体验设计经验和项目管理成果，适用于互联网公司应聘", "description": "第二个示例的优化后文本。"}, "tryExample2Button": {"message": "📝 试试这个例子", "description": "尝试第二个示例的按钮文本。"}, "historyTitle": {"message": "历史记录", "description": "历史记录下拉菜单的标题。"}, "clearHistoryButton": {"message": "清空", "description": "清空历史记录的按钮文本。"}, "confirmClearMessage": {"message": "确定要清空所有历史记录吗？", "description": "清空历史记录的确认信息。"}, "confirmCancelButton": {"message": "取消", "description": "确认对话框中取消按钮的文本。"}, "confirmOkButton": {"message": "确定"}, "languageEnglish": {"message": "English"}, "languageChineseSimplified": {"message": "简体中文"}, "themeLabel": {"message": "主题"}, "themeLightMode": {"message": "浅色模式"}, "themeDarkMode": {"message": "深色模式"}, "themeAutoMode": {"message": "跟随系统"}, "optimizeInputButton": {"message": "优化输入"}, "optimizeAgainButton": {"message": "再次优化"}, "optimizationLimitReached": {"message": "已达优化上限"}, "optimizing": {"message": "优化中..."}, "thinking": {"message": "正在思考中..."}, "listening": {"message": "正在聆听..."}, "waitingForInput": {"message": "正在等待用户输入..."}, "processingFailed": {"message": "处理失败"}, "noHistoryMessage": {"message": "暂无历史记录。"}, "clearHistoryFailed": {"message": "清空失败。"}, "firstOptimizationAnalysis": {"message": "正在进行首次优化与分析..."}, "optimizedPromptTitle": {"message": "✨ 优化后的提示词："}, "problemAnalysisTitle": {"message": "🔬 问题分析："}, "generatingOptimizedPrompt": {"message": "正在生成优化后的提示词..."}, "analyzingInput": {"message": "正在分析输入内容..."}, "structuredPromptTitle": {"message": "✨ 专家模式提示词："}}