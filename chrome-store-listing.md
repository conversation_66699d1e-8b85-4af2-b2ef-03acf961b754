# Chrome Web Store 发布材料

## 📋 基本信息

### 扩展名称
- **英文**: Prompt Enhancer
- **中文**: 点石成金

### 简介
- **英文**: Transform your prompts into gold - Once the extension sidebar is open, when you chat with mainstream AI models like GPT and Deepseek, you can enhance your input prompts. By pasting the enhanced prompts into these AI chat boxes, you will receive more accurate answers.
- **中文**: 提示词增强工具 - 打开扩展侧边栏后，与GPT、deepseek等主流AI聊天时，可以增强你的输入提示词，将增强后的提示词粘贴到这些AI聊天框里，能够得到更加精准的答案。

### 版本号
- **当前版本**: 0.0.1

### 类别
- **主类别**: Productivity (生产力工具)
- **子类别**: Developer Tools (开发者工具)

## 📝 详细描述 (支持Markdown)

### 英文版本

**Transform Your AI Conversations with Intelligent Prompt Enhancement**

Prompt Enhancer is a powerful Chrome extension that revolutionizes how you interact with AI platforms. Whether you're using ChatGPT, Claude, Gemini, or other AI services, our tool helps you craft better prompts for more accurate and useful responses.

## ✨ Key Features

### 🎯 Smart Optimization
- **Automatic Analysis**: Intelligently analyzes your input and suggests improvements
- **Context Enhancement**: Adds relevant context and structure to your prompts
- **Real-time Suggestions**: Get instant feedback on how to improve your prompts

### 🧠 Expert Mode
- **Role-Playing Prompts**: Transform simple ideas into professional system prompts
- **Persona Creation**: Generate detailed expert personas tailored to your needs
- **Complex Task Handling**: Perfect for advanced AI interactions and specialized tasks

### 🎤 Voice Input
- **Hands-free Operation**: Use voice commands to input your ideas
- **Speech Recognition**: Advanced speech-to-text functionality
- **Seamless Integration**: Works across all supported AI platforms

### 🌐 Multi-Platform Support
Works seamlessly with:
- ChatGPT (OpenAI)
- Claude (Anthropic)
- Gemini (Google)
- Copilot (Microsoft)
- DeepSeek
- Poe
- Perplexity

### 🔧 Advanced Features
- **History Tracking**: Keep track of your optimization history
- **Multi-language Support**: Available in English and Chinese
- **Dark/Light Themes**: Customizable interface themes
- **Side Panel Integration**: Non-intrusive side panel design
- **Secure Authentication**: Google OAuth integration for data sync

## 🚀 How It Works

1. **Install & Login**: Install the extension and sign in with your Google account
2. **Navigate to AI Platform**: Visit any supported AI website
3. **Open Side Panel**: Click the extension icon to open the enhancement panel
4. **Input Your Idea**: Type or speak your initial prompt idea
5. **Get Enhanced Prompts**: Receive optimized, professional prompts instantly
6. **Copy & Use**: Copy the enhanced prompt to the AI platform

## 🔒 Privacy & Security

- **Secure Data Handling**: All data is encrypted and securely stored
- **Google OAuth**: Safe authentication through Google's secure system
- **No Data Mining**: We don't sell or share your personal data
- **Local Processing**: Most operations happen locally in your browser

## 💡 Perfect For

- **Content Creators**: Enhance creative writing and content generation
- **Developers**: Improve code-related AI interactions
- **Researchers**: Create better research and analysis prompts
- **Students**: Optimize learning and study-related queries
- **Professionals**: Enhance business and productivity tasks

Transform your AI conversations today with Prompt Enhancer!

### 中文版本

**智能提示词增强，让AI对话更精准高效**

点石成金是一款强大的Chrome扩展程序，彻底改变您与AI平台的交互方式。无论您使用ChatGPT、Claude、Gemini还是其他AI服务，我们的工具都能帮助您制作更好的提示词，获得更准确、更有用的回答。

## ✨ 核心功能

### 🎯 智能优化
- **自动分析**: 智能分析您的输入并提供改进建议
- **上下文增强**: 为您的提示词添加相关上下文和结构
- **实时建议**: 获得如何改进提示词的即时反馈

### 🧠 专家模式
- **角色扮演提示词**: 将简单想法转化为专业系统提示词
- **人设创建**: 生成针对您需求的详细专家人设
- **复杂任务处理**: 完美适用于高级AI交互和专业任务

### 🎤 语音输入
- **解放双手**: 使用语音命令输入您的想法
- **语音识别**: 先进的语音转文字功能
- **无缝集成**: 在所有支持的AI平台上都能正常工作

### 🌐 多平台支持
无缝支持以下平台：
- ChatGPT (OpenAI)
- Claude (Anthropic)
- Gemini (Google)
- Copilot (Microsoft)
- DeepSeek
- Poe
- Perplexity

### 🔧 高级功能
- **历史记录**: 跟踪您的优化历史
- **多语言支持**: 支持中英文界面
- **深色/浅色主题**: 可自定义界面主题
- **侧边栏集成**: 非侵入式侧边栏设计
- **安全认证**: Google OAuth集成，数据同步安全

## 🚀 使用方法

1. **安装并登录**: 安装扩展程序并使用Google账户登录
2. **访问AI平台**: 访问任何支持的AI网站
3. **打开侧边栏**: 点击扩展程序图标打开增强面板
4. **输入想法**: 输入或语音输入您的初始提示想法
5. **获得增强提示词**: 立即获得优化的专业提示词
6. **复制使用**: 将增强后的提示词复制到AI平台使用

## 🔒 隐私与安全

- **安全数据处理**: 所有数据都经过加密并安全存储
- **Google OAuth**: 通过Google安全系统进行安全认证
- **不挖掘数据**: 我们不出售或分享您的个人数据
- **本地处理**: 大部分操作在您的浏览器本地进行

## 💡 适用人群

- **内容创作者**: 增强创意写作和内容生成
- **开发者**: 改进代码相关的AI交互
- **研究人员**: 创建更好的研究和分析提示词
- **学生**: 优化学习和学习相关查询
- **专业人士**: 增强商务和生产力任务

立即使用点石成金，改变您的AI对话体验！

## 🔐 权限说明

### 必需权限及用途

1. **sidePanel**: 
   - **用途**: 创建侧边栏界面，提供非侵入式的用户体验
   - **必要性**: 核心功能实现，无此权限无法正常工作

2. **storage**: 
   - **用途**: 存储用户设置（语言、主题）和历史记录
   - **数据类型**: 用户偏好设置、提示词优化历史
   - **存储位置**: 本地浏览器存储 + Supabase云端同步

3. **identity**: 
   - **用途**: Google OAuth认证，实现安全登录和数据同步
   - **必要性**: 用户身份验证和个人数据保护

4. **host_permissions**: 
   - **涵盖网站**: ChatGPT、Claude、Gemini、Copilot、DeepSeek、Poe、Perplexity
   - **用途**: 在AI平台页面注入内容脚本，实现提示词自动填充功能
   - **数据访问**: 仅访问输入框内容，不读取其他页面数据

### 数据处理声明

- **收集数据**: 用户输入的提示词、优化结果、使用偏好
- **处理目的**: 提供提示词优化服务、保存用户历史记录
- **存储方式**: 加密存储在Supabase云端数据库
- **数据保留**: 用户可随时删除历史记录，注销账户时自动清除所有数据
- **第三方共享**: 不与任何第三方共享用户数据

## 📊 技术规格

- **Manifest版本**: 3
- **最低Chrome版本**: 88+
- **文件大小**: 约2MB
- **支持语言**: 英语、简体中文
- **后端服务**: Supabase (符合GDPR标准)
- **AI服务**: Qwen API (阿里云)



### 常见问题
1. **如何开始使用？**
   - 安装扩展后使用Google账户登录即可

2. **支持哪些AI平台？**
   - ChatGPT、Claude、Gemini、Copilot、DeepSeek、Poe、Perplexity

3. **数据安全吗？**
   - 使用Google OAuth认证，数据加密存储在Supabase

4. **如何删除历史记录？**
   - 在设置菜单中点击"清空历史记录"

5. **支持哪些语言？**
   - 目前支持英语和简体中文

## 🔐 Permission Justifications

### Single Purpose Declaration
**Purpose**: Prompt Enhancer serves a single purpose: to help users create better prompts for AI platforms. All features - optimization, expert mode, history tracking, and multi-platform support - directly contribute to this core functionality of enhancing user prompts for more effective AI interactions.

### Remote Code Usage
**Purpose**: Our extension uses remote code (via Supabase Edge Functions) to process prompt optimizations using AI models that are too large to bundle within the extension. This architecture allows us to provide high-quality prompt enhancements while keeping the extension lightweight and performant. All remote code execution is transparent, secure, and limited to processing user-initiated prompt optimization requests.



