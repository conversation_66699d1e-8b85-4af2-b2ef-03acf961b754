<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>点石成金 Prompt Enhancer - 智能提示词增强器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100vh;
            overflow: hidden;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #ffffff;
        }

        /* 现代化动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }
            50% {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* 现代化玻璃效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        header:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease;
        }

        .logo:hover {
            transform: scale(1.05);
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
        }

        .nav-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-links a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .nav-links a:hover::before {
            left: 100%;
        }

        main {
            margin-top: 80px;
            padding: 1rem 0 0.5rem;
        }

        .hero {
            text-align: center;
            margin-bottom: 2rem;
            padding: 0.5rem 0;
        }

        .hero h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.8rem;
            text-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease 0.2s both;
        }

        .hero p {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1.5rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            text-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease 0.4s both;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.8rem 1.6rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            animation: fadeInUp 1s ease 0.6s both, glow 2s ease-in-out infinite;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .features {
            margin-bottom: 1rem;
        }

        .features .lang-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease both;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.8s;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.95);
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
            border-radius: 12px;
            margin: 0 auto 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
            animation: float 3s ease-in-out infinite;
        }

        .feature-card h3 {
            margin-bottom: 0.4rem;
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            text-shadow: none;
        }

        .feature-card p {
            color: #6b7280;
            line-height: 1.4;
            font-size: 0.8rem;
            text-shadow: none;
        }





        /* 语言切换按钮 */
        .language-toggle {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            padding: 8px 16px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #374151;
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        .language-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.2), transparent);
            transition: left 0.5s;
        }

        .language-toggle:hover {
            background: #e5e7eb;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .language-toggle:hover::before {
            left: 100%;
        }

        .language-toggle.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
            box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
        }

        /* 语言切换内容 */
        .lang-content {
            transition: opacity 0.3s ease;
        }

        .lang-content.hidden {
            display: none;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.25rem;
            color: #6b7280;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        @media (max-width: 768px) {
            main {
                padding: 1rem 0 0.5rem;
            }

            .hero {
                margin-bottom: 2rem;
                padding: 0.5rem 0;
            }

            .hero h1 {
                font-size: 1.8rem;
                margin-bottom: 0.6rem;
            }

            .hero p {
                font-size: 1rem;
                margin-bottom: 1.2rem;
            }

            .nav-links {
                display: none;
            }

            .features {
                margin-bottom: 1rem;
            }

            .features .lang-content {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .feature-card {
                padding: 1rem;
            }

            .feature-card h3 {
                font-size: 0.9rem;
                margin-bottom: 0.3rem;
            }

            .feature-card p {
                font-size: 0.75rem;
                line-height: 1.3;
            }

            .cta-button {
                padding: 0.7rem 1.4rem;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 1024px) and (min-width: 769px) {
            .features .lang-content {
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
            }

            .feature-card {
                padding: 1.25rem;
            }

            .feature-card h3 {
                font-size: 1rem;
            }

            .feature-card p {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">💎</div>
                    <span id="logoText">Prompt Enhancer</span>
                </div>
                <nav class="nav-links">
                    <a href="#features" id="navFeatures">Features</a>
                    <a href="#download" id="navDownload">Download</a>
                    <button class="language-toggle active" id="langToggle">中文</button>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <section class="hero">
                <!-- 中文内容 -->
                <div id="content-zh" class="lang-content hidden">
                    <h1>点石成金</h1>
                    <p>智能提示词增强器 - 让您的AI对话更加精准高效</p>
                    <a href="#download" class="cta-button">在chrome扩展商店搜索点石成金安装</a>
                </div>

                <!-- 英文内容 -->
                <div id="content-en" class="lang-content">
                    <h1>Prompt Enhancer</h1>
                    <p>Transform your prompts into gold - Make AI conversations more precise and efficient</p>
                    <a href="#download" class="cta-button">Search for "Prompt Enhancer" in the Chrome Web Store and install it.</a>
                </div>
            </section>

            <section id="features" class="features">
                <!-- 中文功能介绍 -->
                <div id="features-zh" class="lang-content hidden">
                    <div class="feature-card">
                        <div class="feature-icon">✨</div>
                        <h3>智能优化</h3>
                        <p>自动分析并优化您的提示词，让AI理解更准确，回答更精准</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>专家模式</h3>
                        <p>将简单想法转化为专业的角色扮演系统提示词，创造独特的AI专家</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎤</div>
                        <h3>语音输入</h3>
                        <p>支持语音输入功能，解放双手，让创作更加自由便捷</p>
                    </div>
                </div>

                <!-- 英文功能介绍 -->
                <div id="features-en" class="lang-content">
                    <div class="feature-card">
                        <div class="feature-icon">✨</div>
                        <h3>Smart Optimization</h3>
                        <p>Automatically analyze and optimize your prompts for more accurate AI understanding and precise responses</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>Expert Mode</h3>
                        <p>Transform simple ideas into professional role-playing system prompts, creating unique AI experts</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎤</div>
                        <h3>Voice Input</h3>
                        <p>Support voice input functionality, free your hands for more convenient creation</p>
                    </div>
                </div>
            </section>


        </div>
    </main>



    <script src="landing-page.js"></script>
</body>
</html>
