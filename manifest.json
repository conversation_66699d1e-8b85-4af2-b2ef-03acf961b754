{"manifest_version": 3, "name": "__MSG_extensionName__", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1WtwoYqOqC0TkmJTNMZebuP21WNUozCt6mbUsD7zkRCVqv/J3uW67ltDt7RsxFpwnBV26fssmXsMJDRTa306CJB4Kd1Y5dmkA6qMIiTPbFAWcE4wRwoteoDRkSuyFalKWPEzTv/scC/FS+iT5Srw5GzuV/n5fkwoYfQzFTK1ic59DvuHjCE083B79IeR5Q1bPCLpBoDs0hKvNsnqh+IKaEokCXDBixeFULHVWr+O7ZVxpvVUtiAKg/CrA0XE4BPJKSAw1rev6ZVbLRguoVvLN9q9URqq6mvGvGHEB865qPe+uxnQn34QYwr4afLPEZgU0SRFJY8DT0pJ6sXI9L39NwIDAQAB", "description": "__MSG_extensionDescription__", "default_locale": "en", "version": "0.0.3", "externally_connectable": {"matches": ["https://*.supabase.co/*"]}, "permissions": ["sidePanel", "storage", "identity"], "oauth2": {"client_id": "217122449184-o7cnlbumpkbud9l0ivfq831efuuumm7c.apps.googleusercontent.com", "scopes": ["openid", "email", "profile"]}, "host_permissions": ["*://*.openai.com/*", "*://*.chatgpt.com/*", "*://*.claude.ai/*", "*://*.copilot.microsoft.com/*", "*://*.gemini.google.com/*", "*://*.deepseek.com/*", "*://*.poe.com/*", "*://*.perplexity.ai/*"], "background": {"service_worker": "background.js"}, "action": {"default_title": "__MSG_extensionName__"}, "side_panel": {"default_path": "sidepanel.html"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src https://*.supabase.co"}, "content_scripts": [{"matches": ["*://*.openai.com/*", "*://*.chatgpt.com/*", "*://*.claude.ai/*", "*://*.copilot.microsoft.com/*", "*://*.gemini.google.com/*", "*://*.deepseek.com/*", "*://*.poe.com/*", "*://*.perplexity.ai/*"], "js": ["content.js"], "run_at": "document_idle"}], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["_locales/**/*.json", "landing-page.html"], "matches": ["<all_urls>"]}]}