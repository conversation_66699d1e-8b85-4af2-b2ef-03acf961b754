/* --- 1. 全局与基础样式 --- */
body {
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
margin: 0;
background-color: #f7f8fa;
color: #1f2937;
/* 添加平滑过渡效果 */
transition: background-color 0.3s ease, color 0.3s ease;
}
.container {
padding: 16px;
position: relative;
}
section {
margin-bottom: 20px;
}
h1 { font-size: 20px; }
h3 {
  font-size: 16px;
  margin: 0 0 10px 0;
  color: #111827;
  /* 添加平滑过渡效果 */
  transition: color 0.3s ease;
}
/* --- 2. 认证界面 --- */
#authContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: 2rem 1.5rem;
  text-align: center;
  background: #f9fafb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

#authContainer h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

#authContainer p {
  font-size: 0.95rem;
  color: #6b7280;
  margin-bottom: 2rem;
  font-weight: 400;
}

.auth-card {
  background: transparent;
  border: none;
  border-radius: 16px;
  padding: 2rem 1.5rem;
  max-width: 320px;
  width: 100%;
  margin-top: 1rem;
}

#authMessage {
  margin-top: 1rem;
  font-size: 14px;
  color: #ef4444;
  font-weight: 500;
}

.login-btn-google {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 14px 20px;
  font-size: 15px;
  font-weight: 500;
  color: #374151;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.login-btn-google::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.login-btn-google:hover::before {
  opacity: 1;
}

.login-btn-google:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.login-btn-google:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.login-btn-google svg {
  margin-right: 8px;
  position: relative;
  z-index: 1;
}

.login-btn-google span {
  position: relative;
  z-index: 1;
}

/* 深色主题支持 */
[data-theme="dark"] #authContainer {
  background: #1f2937;
}

[data-theme="dark"] #authContainer h1 {
  color: #f9fafb;
}

[data-theme="dark"] #authContainer p {
  color: #9ca3af;
}

[data-theme="dark"] .auth-card {
  background: transparent;
  border: none;
  color: #f3f4f6;
}

[data-theme="dark"] .login-btn-google {
  background: rgba(75, 85, 99, 0.7);
  backdrop-filter: blur(16px);
  color: #f3f4f6;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

[data-theme="dark"] .login-btn-google::before {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.2) 0%, rgba(156, 163, 175, 0.05) 100%);
}

[data-theme="dark"] .login-btn-google:hover {
  background: rgba(107, 114, 128, 0.9);
  border-color: rgba(156, 163, 175, 0.5);
}

/* 响应式设计 */
@media (max-width: 480px) {
  #authContainer {
    padding: 1.5rem 1rem;
  }

  #authContainer h1 {
    font-size: 1.5rem;
  }

  #authContainer p {
    font-size: 0.9rem;
  }

  .auth-card {
    padding: 1.5rem 1rem;
    max-width: 280px;
  }

  .login-btn-google {
    padding: 10px 16px;
    font-size: 14px;
  }
}
/* --- 3. 主界面 --- */
.user-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.settings-container {
  position: relative;
}

.settings-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 240px;
  z-index: 1000;
  margin-top: 5px;
  /* 添加平滑过渡效果 */
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.settings-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.settings-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.settings-content {
  padding: 12px 16px;
}

.user-info {
  margin-bottom: 12px;
}

.user-email {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-email .label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.user-email span:last-child {
  font-size: 13px;
  color: #374151;
  word-break: break-all;
}

.settings-actions {
  border-top: 1px solid #f3f4f6;
  padding-top: 12px;
  margin-top: 12px;
}

.landing-page-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background-color: #2563eb;
  color: white;
  border: 1px solid #2563eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
}

.landing-page-btn:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.landing-page-btn svg {
  flex-shrink: 0;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  font-size: 13px;
  background-color: #f9fafb;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.logout-btn svg {
  flex-shrink: 0;
}#userEmail { font-size: 12px; color: #4b5563; }
#logoutButton {
padding: 4px 10px; font-size: 12px; background-color: #e5e7eb;
color: #4b5563; border: none; border-radius: 5px; cursor: pointer;
}
#logoutButton:hover { background-color: #d1d5db; }
/* --- 4. 操作按钮 --- */
.actions-container { display: flex; gap: 10px; }
.btn {
padding: 10px 15px; border-radius: 6px; font-size: 15px;
cursor: pointer; border: 1px solid transparent;
/* 增强过渡效果 */
transition: all 0.3s ease;
display: flex; align-items: center; justify-content: center;
}
.btn-primary {
flex-grow: 2; background-color: #3b82f6; color: white;
}
.btn-primary:hover { background-color: #2563eb; }
.btn-primary:disabled { background-color: #93c5fd; cursor: not-allowed; }
.btn-secondary {
flex-grow: 1; background-color: #fff; color: #374151; border-color: #d1d5db;
}
.btn-secondary:hover { background-color: #f9fafb; }
/* --- 5. 结果区域 --- */
.result-header { display: flex; justify-content: space-between; align-items: center; }
.result-block {
background-color: #fff; border: 1px solid #e5e7eb;
border-radius: 8px; padding: 14px; margin-top: 10px;
/* 添加平滑过渡效果 */
transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
.result-block:not(:last-child) { margin-bottom: 14px; }
/* sidepanel.css */

/* --- 2. 结果块的头部 --- */
.result-block-header {
  display: flex;                 /* 1. 让它内部的元素（标题和控件）在同一行排列 */
  justify-content: space-between;  /* 2. 将标题推到最左边，控件推到最右边 */
  align-items: center;             /* 3. 让它们在垂直方向上居中对齐 */
  margin-bottom: 10px;           /* (保留我们需要的下边距) */
  .result-block-header h4 {
  /* 【关键】将默认的外边距完全清零 */
  margin: 0; 
  /* (保留我们需要的其他样式) */
  font-size: 14px;
  color: #1e40af;
  font-weight: 600;
}
}
.readonly-result { font-size: 15px; line-height: 1.6; color: #374151; }

/* 分析结果样式 */
.analysis-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2563eb;
  margin: 8px 0 6px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid #e5e7eb;
}

/* 第一个分析标题不需要上边距 */
.analysis-section-title:first-child {
  margin-top: 0;
}

.analysis-content {
  font-size: 15px;
  line-height: 1.6;
  color: #374151;
  margin: 2px 0;
  padding-left: 8px;
}

/* 第一个分析内容不需要上边距 */
.analysis-content:first-child {
  margin-top: 0;
}

/* 优化结果分段样式 */
.optimized-paragraph {
  font-size: 15px;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 8px;
  text-align: justify;
}

.paragraph-spacing {
  height: 12px;
}
/* sidepanel.css */

/* 可编辑结果的基础样式 */
.editable-result {
  font-size: 15px;
  line-height: 1.6;
  cursor: pointer;
  
  /* 【确保添加或修改这一行】 */
  /* 我们让 padding 的变化也变得平滑 */
  transition: box-shadow 0.2s, padding 0.2s ease-in-out;

  /* 防止内容溢出 */
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  max-width: 100%;
  overflow-x: auto;

  /* 【重要】为了让过渡效果更完美，我们给它一个默认的内边距 */
  /* 当它不是编辑状态时，padding 为 0，点击后变为 8px */
  padding: 0;
}

/* --- 最终版：可编辑状态样式，增加内边距以提升“呼吸感” --- */
.editable-result.editable {
  /* 我们保留之前的所有高亮效果 */
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
  border-radius: 6px; 
  box-shadow: 0 0 8px rgba(96, 165, 250, 0.3);

  /* 【最关键的新增】 */
  /* 为元素增加一点内边距，将文字向内推 */
  padding: 8px; 

  /* 【可选但推荐】为了让padding的变化不那么生硬，我们给它加上一个平滑的过渡效果 */
  /* 注意：这个过渡效果也需要添加到 .editable-result 的基础样式中 */
  transition: box-shadow 0.2s, padding 0.2s ease-in-out;
}
/* --- 6. 头部与结果块内部的图标按钮 --- */
.header-actions, .comparison-controls { display: flex; align-items: center; gap: 8px; }
.header-icon-btn, .comparison-controls .icon-btn {
background: none; border: none;
padding: 4px; border-radius: 5px; cursor: pointer; line-height: 1;
display: flex; align-items: center; justify-content: center;
transition: background-color 0.2s; opacity: 1; visibility: visible;
}
.header-icon-btn:hover, .comparison-controls .icon-btn:hover { background-color: #f3f4f6; }
/* --- 新增：所有图标按钮被点击（激活）时的动效 --- */
.header-icon-btn:active, .comparison-controls .icon-btn:active {
  /* 
   * 【最关键的修改】使用 ease-in-out，这是一个两头慢、中间快的平滑曲线，没有任何抖动。
   animation: button-pop 0.2s ease-in-out;
  /* 2. (可选) 可以给一个不同的背景色作为即时反馈 */
  background-color: #d1d5db; 
}
.header-icon-btn svg, .comparison-controls .icon-btn svg {
stroke: #4b5563; width: 16px; height: 16px;
}
#prev-version-btn, #next-version-btn { font-weight: bold; font-size: 16px; padding: 2px 8px; }
#comparison-pagination {
font-size: 14px; color: #374151; min-width: 50px; text-align: center;
font-family: Consolas, "Courier New", monospace;
}
.comparison-controls .icon-btn:disabled { opacity: 0.4; cursor: not-allowed; }
/* --- 7. 加载动画 --- */
.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(107, 114, 128, 0.3);
  border-radius: 50%;
  border-top-color: #6b7280;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
  display: inline-block;
  vertical-align: middle;
}

/* 深色模式下的spinner样式 */
body.dark-theme .spinner {
  border-color: rgba(156, 163, 175, 0.3);
  border-top-color: #9ca3af;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 加载状态容器样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

.loading-text {
  margin-left: 8px;
  vertical-align: middle;
}

/* 深色模式下的加载状态 */
body.dark-theme .loading-state {
  color: #9ca3af;
}


/* --- 8. 历史记录下拉菜单 --- */
.dropdown-menu {
  display: none;
  position: fixed;
  top: 95px;
  right: 16px;
  width: 320px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 8px 10px -6px rgba(0,0,0,0.1);
  z-index: 1000;
  padding: 16px;
  max-height: 350px;
  overflow: hidden;
}

/* 当侧边栏较窄时，调整弹窗位置 */
@media (max-width: 400px) {
  .dropdown-menu {
    position: fixed;
    top: 95px;
    left: 8px;
    right: 8px;
    width: auto;
    max-width: none;
  }
}
.dropdown-menu.active { display: block; }

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

#clearHistoryBtn {
  padding: 6px 12px;
  font-size: 12px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}
#clearHistoryBtn:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 4px;
}

/* 历史记录滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.history-item {
  background-color: #f9fafb;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  padding: 14px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.history-item:hover {
  background-color: #f3f4f6;
  border-color: #e5e7eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-item p {
  margin: 0 0 8px 0;
  font-size: 13px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item p strong {
  color: #111827;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.history-item .timestamp {
  font-size: 11px;
  color: #6b7280;
  text-align: right;
  margin-top: 8px;
  font-weight: 500;
}

.history-empty-message {
  text-align: center;
  color: #9ca3af;
  padding: 40px 20px;
  font-size: 14px;
}

/* sidepanel.css (添加到文件末尾) */

/* --- 9. 【新增】自定义确认对话框样式 --- */

/* 遮罩层：覆盖整个侧边栏，半透明背景 */
.confirm-overlay {
  position: fixed; /* 固定定位，覆盖整个视口 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(17, 24, 39, 0.4); /* 半透明的深灰色，比纯黑更柔和 */
  display: none; /* 默认隐藏 */
  align-items: center;
  justify-content: center;
  z-index: 2000; /* 确保在最顶层 */
  padding: 16px;
}

/* 当对话框被激活时，使用 flex 布局来显示它 */
.confirm-overlay.active {
  display: flex;
}

/* 对话框本身 */
.confirm-box {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 300px;
}

/* 对话框中的消息文本 */
.confirm-message {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #1f2937;
}

/* 按钮容器 */
.confirm-actions {
  display: flex;
  gap: 12px;
}

/* 确认按钮（危险操作，用红色）*/
.btn-danger {
    background-color: #ef4444; /* 红色 */
    color: white;
}
.btn-danger:hover {
    background-color: #dc2626; /* 深红色 */
}

/* sidepanel.css (添加到文件末尾) */

/* --- 新增：定义“按钮弹出”的微动效 --- */
@keyframes button-pop {
  /* 动画开始时 (0%) - 原始状态 */
  0% {
    transform: scale(1);
    opacity: 1;
  }
  
  /* 动画进行到一半时 (50%) - 稍微缩小并变淡，像在“吸气” */
  50% {
    transform: scale(0.92);
    opacity: 0.7;
  }

  /* 动画结束时 (100%) - 恢复原始状态 */
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* sidepanel.css (添加到文件末尾) */

/* --- 12. 【新增】语音输入按钮与状态样式 --- */

/* 用于布局标题和麦克风按钮的容器 */
.user-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px; /* 保持和原 h3 的下边距一致 */
}
.user-input-header h3 {
  margin: 0; /* 移除 h3 的默认边距 */
}

/* 【关键】当按钮处于“正在聆听”状态时 */
#voice-input-btn.is-listening {
  /* 应用一个名为 "pulse" 的动画 */
  animation: pulse 1.5s infinite;
  /* 将图标颜色变为危险红色，给予明确反馈 */
  color: #ef4444; 
}

#voice-input-btn.is-listening svg {
  stroke: #ef4444; /* 同样改变 SVG 图标的颜色 */
}

/* 定义 "pulse" 动画：一个柔和的、呼吸般的红色辉光 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* --- 新增：可编辑输入框样式 --- */
.editable-input {
  font-size: 15px;
  line-height: 1.6;
  cursor: pointer;
  padding: 0;
  background-color: #f8f9fa;
  border: none;
  border-radius: 6px;
  /* 增强过渡效果，包含主题切换 */
  transition: box-shadow 0.3s ease, padding 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  min-height: 20px;
  max-height: 120px; /* 限制最大高度 */
  overflow-y: auto; /* 超出时显示垂直滚动条 */
  word-wrap: break-word;
}

.editable-input:hover {
  background-color: #f1f3f4;
}

/* 可编辑状态样式 - 借鉴生成结果的样式 */
.editable-input.editable {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
  border-radius: 6px;
  box-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
  background-color: #ffffff;
  padding: 8px;
  max-height: 120px; /* 编辑状态也限制最大高度 */
  overflow-y: auto; /* 编辑状态也显示滚动条 */
  transition: box-shadow 0.2s, padding 0.2s ease-in-out;
}

/* 当内容为默认提示文字时的样式 */
.editable-input.placeholder {
  color: #9ca3af;
  font-style: italic;
}
.empty-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
  margin: 20px 0;
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 12px;
  opacity: 0.6;
}

.empty-state h4 {
  color: #6b7280;
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: normal;
}

.empty-state > p {
  margin: 8px 0 16px 0;
  line-height: 1.4;
  font-size: 14px;
}

.empty-state > p.tip {
  font-style: italic !important;
}

.example-prompts {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 16px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.example-item {
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  text-align: left;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.example-item:hover {
  border-color: #d1d5db;
}

.example-before, .example-after {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.3;
}

.example-before .label {
  color: #9ca3af;
  font-weight: normal;
  margin-right: 6px;
}

.example-after .label {
  color: #6b7280;
  font-weight: normal;
  margin-right: 6px;
}

.example-before .text {
  color: #9ca3af;
  font-style: normal;
}

.example-after .text {
  color: #6b7280;
  font-weight: normal;
}

.example-btn {
  display: none;
}

.empty-state .tip {
  background-color: transparent;
  color: #9ca3af;
  padding: 8px;
  border-radius: 4px;
  margin-top: 16px;
  font-size: 13px;
  border: none;
  border-left: 2px solid #e5e7eb;
  font-style: italic !important;
}
.language-selector-section {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.language-selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.language-selector-item .label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.language-selector {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 11px;
  color: #374151;
  cursor: pointer;
  min-width: 120px;
  flex: 1;
}

/* 主题选择器样式 */
.theme-toggle-section {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.theme-selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.theme-selector-item .label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.theme-selector {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 11px;
  color: #374151;
  cursor: pointer;
  min-width: 120px;
  flex: 1;
}

/* 深色主题样式 */
body.dark-theme .language-selector-section {
  border-bottom-color: #4b5563;
}

body.dark-theme .language-selector {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

body.dark-theme .theme-toggle-section {
  border-bottom-color: #4b5563;
}

body.dark-theme .theme-selector-item .label {
  color: #9ca3af;
}

body.dark-theme .theme-selector {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

/* 深色主题 - 全局样式 */
body.dark-theme {
  background-color: #0f172a; /* 更深的背景色 */
  color: #e2e8f0; /* 柔和的浅灰色文字 */
}

body.dark-theme .container {
  background-color: #0f172a;
}

body.dark-theme h3 {
  color: #f1f5f9; /* 稍微亮一点的标题色 */
}

/* 深色主题 - 用户状态区域 */
body.dark-theme .user-status {
  border-bottom-color: #374151;
}

/* 深色主题 - 设置下拉菜单 */
body.dark-theme .settings-dropdown {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

body.dark-theme .settings-header {
  border-bottom-color: #374151;
}

body.dark-theme .settings-header h4 {
  color: #f9fafb;
}

body.dark-theme .user-info .label {
  color: #9ca3af;
}

body.dark-theme .user-info span:not(.label) {
  color: #f9fafb;
}

/* 深色主题 - 按钮样式 */
body.dark-theme .btn-primary {
  background-color: #4338ca; /* 更深沉的靛蓝色 */
  color: #ffffff;
  border-color: #4338ca;
}

body.dark-theme .btn-primary:hover {
  background-color: #3730a3; /* 悬停时更深 */
}

body.dark-theme .btn-primary:disabled {
  background-color: #6b7280; /* 禁用状态使用中性灰色 */
  color: #9ca3af;
}

body.dark-theme .btn-secondary {
  background-color: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

body.dark-theme .btn-secondary:hover {
  background-color: #4b5563;
}

body.dark-theme .landing-page-btn {
  background-color: #4338ca; /* 与优化输入按钮保持一致的紫色 */
  color: white;
  border-color: #4338ca;
}

body.dark-theme .landing-page-btn:hover {
  background-color: #3730a3; /* 悬停时更深的紫色 */
  border-color: #3730a3;
}

body.dark-theme .logout-btn {
  background-color: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

body.dark-theme .logout-btn:hover {
  background-color: #4b5563;
}

/* 深色主题 - 输入框 */
body.dark-theme .editable-input {
  background-color: transparent; /* 透明背景，融入深色主题 */
  color: #e5e7eb; /* 浅灰色文字 */
  border: none; /* 移除边框 */
}

body.dark-theme .editable-input:hover {
  background-color: rgba(55, 65, 81, 0.3); /* 悬停时轻微的半透明背景 */
}

body.dark-theme .editable-input.editable {
  background-color: #1f2937; /* 编辑状态时才显示背景 */
  border: 1px solid #4338ca; /* 编辑状态时显示边框 */
  border-radius: 6px;
  padding: 8px;
  max-height: 120px; /* 深色主题编辑状态也限制最大高度 */
  overflow-y: auto; /* 深色主题编辑状态也显示滚动条 */
  box-shadow: 0 0 8px rgba(67, 56, 202, 0.3); /* 靛蓝色阴影 */
}

body.dark-theme .editable-input.placeholder {
  color: #6b7280; /* 中等灰色占位符 */
  font-style: italic;
  background-color: transparent; /* 占位符状态完全透明 */
  border: none; /* 占位符状态无边框 */
}

/* 输入框滚动条样式 */
.editable-input::-webkit-scrollbar {
  width: 6px;
}

.editable-input::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.editable-input::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.editable-input::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 深色主题滚动条样式 */
body.dark-theme .editable-input::-webkit-scrollbar-track {
  background: #374151;
}

body.dark-theme .editable-input::-webkit-scrollbar-thumb {
  background: #6b7280;
}

body.dark-theme .editable-input::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 深色主题 - 结果区域 */
body.dark-theme .result-block {
  background-color: #1f2937; /* 深灰色背景 */
  border-color: #374151; /* 稍亮的深灰色边框 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* 增加微妙阴影 */
}

body.dark-theme .result-block h4 {
  color: #f3f4f6; /* 浅灰色标题 */
}

body.dark-theme .editable-result {
  color: #e5e7eb; /* 浅灰色文本 */
  background-color: transparent;
}

body.dark-theme .readonly-result {
  color: #d1d5db; /* 中等浅灰色 */
}

/* 深色主题 - 分析结果样式 */
body.dark-theme .analysis-section-title {
  color: #60a5fa; /* 浅蓝色 */
  border-bottom-color: #374151;
}

body.dark-theme .analysis-content {
  color: #d1d5db;
}

/* 深色主题 - 优化结果分段样式 */
body.dark-theme .optimized-paragraph {
  color: #d1d5db;
}

/* 深色主题 - 空状态样式 */
body.dark-theme .empty-state {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%); /* 深色渐变背景 */
  border: 1px solid #374151; /* 深色边框 */
  color: #e5e7eb; /* 浅色文字 */
}

body.dark-theme .empty-state .tip {
  color: #9ca3af; /* 中等灰色提示 */
  border-left: none; /* 移除左边框，避免在深色主题下突兀 */
}

/* 深色主题 - 示例项目样式 */
body.dark-theme .example-item {
  background-color: #374151; /* 深灰色背景 */
  border-color: #4b5563; /* 深色边框 */
  color: #e5e7eb; /* 浅色文字 */
}

body.dark-theme .example-item:hover {
  border-color: #6b7280; /* 悬停时稍亮的边框 */
  background-color: #4b5563; /* 悬停时稍亮的背景 */
}

/* 深色主题 - 示例标签样式 */
body.dark-theme .example-before .label {
  color: #9ca3af; /* 中等灰色标签 */
}

body.dark-theme .example-after .label {
  color: #d1d5db; /* 稍亮的灰色标签 */
}

body.dark-theme .example-before .text {
  color: #9ca3af; /* 优化前文本 */
}

body.dark-theme .example-after .text {
  color: #e5e7eb; /* 优化后文本，更亮一些 */
}

/* 深色主题 - 示例内容样式优化 */
body.dark-theme .result-block .editable-result {
  line-height: 1.6;
}

/* 为示例内容添加分隔效果 */
body.dark-theme .result-block .editable-result br + br {
  border-top: 1px solid #374151;
  margin: 12px 0;
  display: block;
  content: "";
}

/* 深色主题 - 历史记录下拉框 */
body.dark-theme .dropdown-menu {
  background-color: #1f2937 !important;
  border: 1px solid #374151 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.3) !important;
}

body.dark-theme .history-header {
  border-bottom: 1px solid #374151;
}

body.dark-theme .history-header h3 {
  color: #f9fafb;
}

/* 深色主题 - 历史记录项目 */
body.dark-theme .history-item {
  background-color: #374151;
  border: 1px solid #4b5563;
  color: #e5e7eb;
}

body.dark-theme .history-item:hover {
  background-color: #4b5563;
  border-color: #6b7280;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
}

body.dark-theme .history-item p {
  color: #e5e7eb;
}

body.dark-theme .history-item p strong {
  color: #f9fafb;
}

body.dark-theme .history-item .timestamp {
  color: #9ca3af;
}

body.dark-theme .history-empty-message {
  color: #9ca3af;
}

/* 深色主题 - 清除历史按钮 */
body.dark-theme #clearHistoryBtn {
  background-color: #374151;
  color: #e5e7eb;
  border-color: #4b5563;
}

body.dark-theme #clearHistoryBtn:hover {
  background-color: #4b5563;
  border-color: #6b7280;
}

/* 深色主题 - 历史记录滚动条 */
body.dark-theme .history-list::-webkit-scrollbar-track {
  background: #374151;
}

body.dark-theme .history-list::-webkit-scrollbar-thumb {
  background: #4b5563;
}

body.dark-theme .history-list::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}



/* 结构化提示词格式化样式 */
.structure-title-h1 {
  color: #2563eb;
  font-weight: bold;
  margin: 12px 0 6px 0;
}

.structure-title-h2 {
  color: #2563eb;
  font-weight: bold;
  margin: 8px 0 4px 0;
}

.structure-list-item {
  margin: 4px 0;
  padding-left: 16px;
  line-height: 1.5;
  color: #374151;
}

.structure-paragraph {
  margin: 6px 0;
  line-height: 1.6;
  color: #374151;
}

.structure-bold {
  color: #1f2937;
  font-weight: 600;
}

/* 深色主题 - 结构化提示词格式化样式 */
body.dark-theme .structure-title-h1 {
  color: #60a5fa;
}

body.dark-theme .structure-title-h2 {
  color: #60a5fa;
}

body.dark-theme .structure-list-item {
  color: #e5e7eb;
}

body.dark-theme .structure-paragraph {
  color: #e5e7eb;
}

body.dark-theme .structure-bold {
  color: #f9fafb;
}

/* 兼容旧样式 */
body.dark-theme .editable-result h3 {
  color: #60a5fa !important;
}

body.dark-theme .editable-result h4 {
  color: #818cf8 !important;
}

body.dark-theme .editable-result strong {
  color: #f9fafb !important;
}

body.dark-theme .editable-result p {
  color: #e5e7eb !important;
}

body.dark-theme .clear-history-btn {
  background-color: #dc2626;
  color: white;
}

body.dark-theme .clear-history-btn:hover {
  background-color: #b91c1c;
}

/* 深色主题 - 确认对话框 */
body.dark-theme .confirm-overlay {
  background-color: rgba(0, 0, 0, 0.6);
}

body.dark-theme .confirm-box {
  background-color: #1f2937;
  border: 1px solid #374151;
}

body.dark-theme .confirm-message {
  color: #f9fafb;
}

body.dark-theme .btn-danger {
  background-color: #dc2626;
  color: white;
}

body.dark-theme .btn-danger:hover {
  background-color: #b91c1c;
}

/* 深色主题 - 图标按钮 */
body.dark-theme .header-icon-btn {
  color: #ffffff; /* 直接使用白色 */
}

body.dark-theme .header-icon-btn:hover {
  color: #e5e7eb; /* 悬停时稍微暗一点 */
}

body.dark-theme .icon-btn {
  color: #ffffff; /* 直接使用白色 */
}

body.dark-theme .icon-btn:hover {
  color: #e5e7eb; /* 悬停时稍微暗一点 */
}