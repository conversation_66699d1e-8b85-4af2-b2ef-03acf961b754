/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    overflow-x: hidden;
}

/* 动态背景 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    animation: gradientShift 10s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    }
    50% {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
    }
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 10%;
    right: 50%;
    animation-delay: 3s;
}

/* 动画定义 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
    }
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

header:hover {
    background: rgba(255, 255, 255, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease;
}

.logo:hover {
    transform: scale(1.05);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    animation: float 3s ease-in-out infinite;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.nav-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-links a:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.nav-links a:hover::before {
    left: 100%;
}

/* 语言切换按钮 */
.language-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 8px 16px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.language-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.language-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
    color: white;
}

.language-toggle:hover::before {
    left: 100%;
}

.language-toggle.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

/* 主要内容 */
main {
    margin-top: 80px;
    padding: 2rem 0;
}

/* 语言切换内容 */
.lang-content {
    transition: opacity 0.3s ease;
}

.lang-content.hidden {
    display: none !important;
}

/* 英雄区域 */
.hero {
    text-align: center;
    margin-bottom: 4rem;
    padding: 2rem 0;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease 0.2s both;
}

.hero p {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease 0.4s both;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    animation: fadeInUp 1s ease 0.6s both, glow 2s ease-in-out infinite;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.cta-button:hover::before {
    left: 100%;
}

/* 功能区域 */
.features {
    margin-bottom: 4rem;
}

.features .lang-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 1s ease both;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.95);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    border-radius: 16px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    animation: float 4s ease-in-out infinite;
}

/* 为不同的图标添加延迟，让动画更自然 */
.feature-card:nth-child(1) .feature-icon {
    animation-delay: 0s;
}

.feature-card:nth-child(2) .feature-icon {
    animation-delay: 1.3s;
}

.feature-card:nth-child(3) .feature-icon {
    animation-delay: 2.6s;
}

.feature-card h3 {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
    font-size: 1rem;
}

/* 定价区域 */
.pricing-section {
    margin-bottom: 4rem;
    text-align: center;
}

.pricing-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.pricing-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    text-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.pricing-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.pricing-card.featured {
    border: 2px solid #667eea;
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.popular-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0 0 12px 12px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.pricing-header {
    margin-bottom: 2rem;
}

.pricing-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 1rem;
}

.currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: #667eea;
}

.amount {
    font-size: 3rem;
    font-weight: 800;
    color: #1f2937;
    margin: 0 0.2rem;
}

.period {
    font-size: 1rem;
    color: #6b7280;
    font-weight: 500;
}

.custom-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    text-align: center;
    padding: 1rem 0;
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.features-list li {
    padding: 0.5rem 0;
    color: #374151;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.features-list li:before {
    content: '';
    width: 20px;
    height: 20px;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.pricing-btn {
    width: 100%;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.free-btn {
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
    color: #374151;
}

.free-btn:hover {
    background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
    transform: translateY(-2px);
}

.pro-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.pro-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.enterprise-btn {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(31, 41, 55, 0.3);
}

.enterprise-btn:hover {
    background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(31, 41, 55, 0.4);
}

/* PayPal按钮容器样式 */
[id^="paypal-button-container"] {
    margin-top: 10px;
}

/* 下载区域 */
.download-section {
    margin-bottom: 4rem;
    text-align: center;
}

.download-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 2rem;
    text-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.download-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.step {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.step:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.step-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #6b7280;
    line-height: 1.6;
}

/* 页脚 */
footer {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 3rem 0 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section p {
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .features .lang-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-10px);
    }

    .download-steps {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links {
        align-items: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .feature-card,
    .step {
        padding: 1.5rem;
    }
}
